package com.faw.work.ais.aic.model.request;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;


/**
 * POC测试请求参数
 * <AUTHOR>
 */
@Data
@Schema(description = "POC测试请求参数")
public class PocTestRequest {

    /**
     * 消息队列ID
     */
    @NotBlank(message = "消息队列ID不能为空")
    @Schema(description = "消息队列ID", example = "123456")
    private String messageQueueId;
}
