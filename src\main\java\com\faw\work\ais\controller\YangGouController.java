package com.faw.work.ais.controller;

import com.dcp.common.rest.Result;
import com.faw.work.ais.service.YangGouService;
import com.faw.work.ais.common.Response;
import com.faw.work.ais.common.dto.DogRequest;
import com.faw.work.ais.common.dto.DogResponse;
import com.faw.work.ais.entity.dto.OpsPromptGenerationDto;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/yanggou")
public class YangGouController {

    @Autowired
    private YangGouService yangGouService;

    @Operation(summary = "每天一次，更新模板", description = "[author:10191705]")
    @PostMapping(value = "/updateTemplate")
    public Response<String> updateTemplate(@RequestBody List<OpsPromptGenerationDto> opsPromptGenerationDtoList) {
        yangGouService.updateTemplate(opsPromptGenerationDtoList);
        return Response.success("接口已响应，任务后台处理中");
    }

    /**
     * 养狗策略生成
     */
    @Operation(summary = "养狗策略生成", description = "[author:10207439]")
    @RequestMapping(value = "/generate", method = RequestMethod.POST)
    public Result<DogResponse> generate(@RequestBody DogRequest request) {

        DogResponse dogResponse = yangGouService.generate(request.getCallText());
        return Result.success(dogResponse);

    }
}
