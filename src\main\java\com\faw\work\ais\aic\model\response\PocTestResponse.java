package com.faw.work.ais.aic.model.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * POC测试响应结果
 * <AUTHOR>
 */
@Data
@Schema(description = "POC测试响应结果")
public class PocTestResponse {

    /**
     * 处理的记录数量
     */
    @Schema(description = "处理的记录数量", example = "10")
    private Integer processedCount;

    /**
     * 处理结果消息
     */
    @Schema(description = "处理结果消息", example = "POC测试完成")
    private String message;
}
