package com.faw.work.ais.aic.model.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * POC测试临时表
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("temp2")
public class Temp2PO implements Serializable {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 内容
     */
    @TableField("content")
    private String content;

    /**
     * 情绪
     */
    @TableField("emotion")
    private String emotion;

    /**
     * 情绪原因
     */
    @TableField("emotion_reason")
    private String emotionReason;

    /**
     * 需求标签
     */
    @TableField("demand_tags")
    private String demandTags;

    /**
     * 问题总结
     */
    @TableField("topic_summary")
    private String topicSummary;
}
