package com.faw.work.ais.aic.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.cloud.ai.dashscope.chat.DashScopeChatOptions;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.faw.work.ais.aic.common.enums.LLMBizTypeEnum;
import com.faw.work.ais.aic.common.enums.MessageStatus;
import com.faw.work.ais.aic.common.enums.MessageTypeEnum;
import com.faw.work.ais.aic.common.mq.MessageProducer;
import com.faw.work.ais.aic.common.util.BaiLianUtils;
import com.faw.work.ais.aic.common.util.DateUtils;
import com.faw.work.ais.aic.common.util.StrUtils;
import com.faw.work.ais.aic.config.BaiLianAppConfig;
import com.faw.work.ais.aic.feign.DgwOpenApiFeignClient;
import com.faw.work.ais.aic.feign.dto.DgwResult;
import com.faw.work.ais.aic.mapper.MessageQueueMapper;
import com.faw.work.ais.aic.mapper.llm.LlmRecordMapper;
import com.faw.work.ais.aic.model.domain.LlmRecord;
import com.faw.work.ais.aic.model.domain.MessageQueue;
import com.faw.work.ais.aic.model.dto.EmotionResponseDTO;
import com.faw.work.ais.aic.model.dto.EmotionResponseSummaryDTO;
import com.faw.work.ais.aic.model.dto.TagAnalysisDTO;
import com.faw.work.ais.aic.model.dto.TopicSummaryDTO;
import com.faw.work.ais.aic.model.request.AiRequest;
import com.faw.work.ais.aic.model.request.ProcessRequest;
import com.faw.work.ais.aic.model.request.RetryFailedMessagesRequest;
import com.faw.work.ais.aic.model.response.AiResponse;
import com.faw.work.ais.aic.model.response.DmsEmotionResponse;
import com.faw.work.ais.aic.model.response.MessageQueueCleanResponse;
import com.faw.work.ais.aic.model.response.RetryFailedMessagesResponse;
import com.faw.work.ais.aic.service.LlmRecordService;
import com.faw.work.ais.aic.service.MessageQueueService;
import com.faw.work.ais.common.exception.BizException;
import com.zaxxer.hikari.HikariDataSource;
import com.zaxxer.hikari.HikariPoolMXBean;
import lombok.extern.slf4j.Slf4j;
import org.mozilla.universalchardet.UniversalDetector;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.sql.DataSource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 情绪分析表 服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class LlmRecordServiceImpl extends ServiceImpl<LlmRecordMapper, LlmRecord> implements LlmRecordService {

    private static final String STATUS_UNPROCESSED = "00";
    private static final String STATUS_PROCESSING = "01";
    private static final String STATUS_COMPLETED = "02";
    private static final String STATUS_FAILED = "10";
    @Autowired
    private DgwOpenApiFeignClient dgwFeignClient;
    @Autowired
    private ChatClient chatClient;
    @Autowired
    private LlmRecordMapper llmRecordMapper;
    @Autowired
    private MessageProducer messageProducer;
    @Autowired
    private BaiLianAppConfig baiLianAppConfig;
    @Autowired
    private MessageQueueMapper messageQueueMapper;

    @Autowired
    private LlmRecordService self;
    @Autowired
    private Executor dmsEmotionExecutor;
    @Autowired
    private MessageQueueService messageQueueService;
    @Autowired
    @Qualifier("retryExecutor")
    private Executor retryExecutor;
    @Autowired
    @Qualifier("aisDatasource")
    private DataSource dataSource;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processConversation(ProcessRequest request) {

        try {
            String requestId = request.getRequestId();
            List<LlmRecord> llmRecords = llmRecordMapper.selectByRequestId(requestId, null, null);
            if (CollUtil.isNotEmpty(llmRecords)) {
                throw new IllegalArgumentException("请求ID已存在，请勿重复提交");
            }

            String conversationContent;
            String textUrl = request.getTextUrl();

            if (StrUtil.isNotBlank(textUrl)) {
                conversationContent = downloadAndReadCosFile(textUrl);
            } else {
                conversationContent = request.getUserInput();
            }

            if (StrUtil.isBlank(conversationContent)) {
                throw new IllegalArgumentException("用户输入不能为空");
            }
            // TODO: 调用大模型根据对话内容分析说话角色

            String customerSpeaker = "客户";
            // 分片客户对话内容
            List<String> slices = StrUtils.sliceConversation(conversationContent, customerSpeaker, 5, 1);
            if (CollUtil.isEmpty(slices)) {
                throw new IllegalArgumentException("对话内容中没有客户的说话内容，分片为空");
            }

            List<LlmRecord> emotionList = new ArrayList<>();
            String now = DateUtils.getCurrentDateTimeString();
            for (String slice : slices) {
                LlmRecord emotion = new LlmRecord();
                emotion.setRequestId(requestId);
                emotion.setUserInput(slice);
                emotion.setStatus(STATUS_UNPROCESSED);
                emotion.setBizType(LLMBizTypeEnum.DMS_EMOTION.getCode());
                emotion.setCallbackUrl("http://localhost:8080/api/v1/llm/emotion/callback");
                emotion.setField1(textUrl);
                emotion.setField2(request.getAudioUrl());
                emotion.setCreateAt(now);
                emotionList.add(emotion);
            }

            this.saveBatch(emotionList);
            log.info("成功将 {} 个情绪分析对话分片存入数据库", emotionList.size());

            List<String> productSlices = StrUtils.sliceConversation(conversationContent, customerSpeaker, 5, 1);
            List<LlmRecord> productList = new ArrayList<>();
            for (String productSlice : productSlices) {
                LlmRecord emotion = new LlmRecord();
                emotion.setRequestId(requestId);
                emotion.setUserInput(productSlice);
                emotion.setStatus(STATUS_UNPROCESSED);
                emotion.setBizType(LLMBizTypeEnum.DMS_PRODUCT.getCode());
                emotion.setCallbackUrl("http://localhost:8080/api/v1/llm/emotion/callback");
                emotion.setField1(textUrl);
                emotion.setField2(request.getAudioUrl());
                emotion.setCreateAt(now);
                productList.add(emotion);
            }

            this.saveBatch(productList);
            log.info("成功将 {} 个产品需求分析对话分片存入数据库", productList.size());

            // 发送MQ消息
            // messageProducer.sendMessage(requestId, MessageTypeEnum.DMS_EMOTION_PRODUCT.getCode());

        } catch (Exception e) {
            log.error("处理请求失败", e);
            throw new BizException(e.getMessage());
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean processSlice(String requestId) {
        List<LlmRecord> list = llmRecordMapper.selectByRequestId(requestId, STATUS_UNPROCESSED, null);

        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (LlmRecord slice : list) {
            // 更新状态为处理中
            log.info("开始处理分片ID: {}", slice.getId());
            self.updateStatusTran(slice.getId(), STATUS_PROCESSING, null);

            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    // 串行执行情绪分析和标签提取
                    if (slice.getBizType().equals(LLMBizTypeEnum.DMS_EMOTION.getCode())) {
                        performEmotionAnalysis(slice);
                    }
                    if (slice.getBizType().equals(LLMBizTypeEnum.DMS_PRODUCT.getCode())) {
                        performTagExtraction(slice);
                    }

                    self.updateStatusTran(slice.getId(), STATUS_COMPLETED, null);
                    log.info("分片ID: {} 处理成功", slice.getId());
                } catch (Exception e) {
                    log.error("分片ID: {} 处理失败，可能是大模型限流了", slice.getId(), e);
                    self.updateStatusTran(slice.getId(), STATUS_FAILED, null);
                }
            }, dmsEmotionExecutor);

            futures.add(future);
        }

        // 等待所有异步任务完成
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        } catch (Exception e) {
            log.error("等待异步任务完成时发生异常", e);
            return false;
        }

        // 调用话题总结模型
        TopicSummaryDTO topicSummaryDTO = performTopicSummary(requestId);
        // 组装数据
        DmsEmotionResponse responseDTO = new DmsEmotionResponse();
        // 填充情绪分析结果
        EmotionResponseSummaryDTO emotion = new EmotionResponseSummaryDTO();
        emotion.setEmotionSummary(topicSummaryDTO.getTotalEmotion());

        List<LlmRecord> finishEmotionList = llmRecordMapper.selectByRequestId(requestId, STATUS_COMPLETED, LLMBizTypeEnum.DMS_EMOTION.getCode());
        List<EmotionResponseDTO> emotionList = new ArrayList<>();
        for (LlmRecord llmRecord : finishEmotionList) {
            EmotionResponseDTO emotionResponseDTO = JSONUtil.toBean(llmRecord.getLlmOutput(), EmotionResponseDTO.class);
            emotionResponseDTO.setInput(llmRecord.getUserInput());
            emotionList.add(emotionResponseDTO);
        }
        emotion.setEmotionList(emotionList);


        responseDTO.setEmotionData(emotion);

        // 填充标签提取结果
        List<LlmRecord> finishTagList = llmRecordMapper.selectByRequestId(requestId, STATUS_COMPLETED, LLMBizTypeEnum.DMS_PRODUCT.getCode());

        List<TagAnalysisDTO> tagData = new ArrayList<>();
        for (LlmRecord llmRecord : finishTagList) {
            TagAnalysisDTO tag = JSONUtil.toBean(llmRecord.getLlmOutput(), TagAnalysisDTO.class);
            tag.setLlmRecordId(llmRecord.getId());
            tag.setInput(llmRecord.getUserInput());
            List<TagAnalysisDTO.TopicSummary> customerQuestionSummaries = tag.getCustomerQuestionSummaries();
            // 过滤掉question或answer为空的记录
            customerQuestionSummaries.removeIf(summary -> StrUtil.isBlank(summary.getQuestion()) || StrUtil.isBlank(summary.getAnswer()));
            // 过滤掉question或answer过短的记录
            tag.setTopicSummaries(customerQuestionSummaries);
            tagData.add(tag);
        }
        responseDTO.setTagData(tagData);
        responseDTO.setTopicData(topicSummaryDTO);
        responseDTO.setRequestId(requestId);
        // 回调接口
        log.info("开始回调接口, 请求参数为: {}", JSONUtil.toJsonStr(responseDTO));

        DgwResult dgwResult = dgwFeignClient.callBackDmsEmotion(responseDTO);

        log.info("回调接口成功, 接口返回: {}", JSONUtil.toJsonStr(dgwResult));



        return true;
    }

    /**
     * 组装数据并发送给调用方
     *
     * @param requestId 请求ID
     * @return 是否发送成功
     */
    private boolean assembleAndSendResponse(String requestId) {
        log.info("开始组装数据并发送给调用方，请求ID: {}", requestId);

        try {
            // 调用话题总结模型
            TopicSummaryDTO topicSummaryDTO = performTopicSummary(requestId);

            // 组装数据
            DmsEmotionResponse responseDTO = new DmsEmotionResponse();

            // 填充情绪分析结果
            EmotionResponseSummaryDTO emotion = new EmotionResponseSummaryDTO();
            emotion.setEmotionSummary(topicSummaryDTO.getTotalEmotion());

            List<LlmRecord> finishEmotionList = llmRecordMapper.selectByRequestId(requestId, STATUS_COMPLETED, LLMBizTypeEnum.DMS_EMOTION.getCode());
            List<EmotionResponseDTO> emotionList = new ArrayList<>();
            for (LlmRecord llmRecord : finishEmotionList) {
                EmotionResponseDTO emotionResponseDTO = JSONUtil.toBean(llmRecord.getLlmOutput(), EmotionResponseDTO.class);
                emotionResponseDTO.setInput(llmRecord.getUserInput());
                emotionList.add(emotionResponseDTO);
            }
            emotion.setEmotionList(emotionList);
            responseDTO.setEmotionData(emotion);

            // 填充标签提取结果
            List<LlmRecord> finishTagList = llmRecordMapper.selectByRequestId(requestId, STATUS_COMPLETED, LLMBizTypeEnum.DMS_PRODUCT.getCode());
            List<TagAnalysisDTO> tagData = new ArrayList<>();
            for (LlmRecord llmRecord : finishTagList) {
                TagAnalysisDTO tag = JSONUtil.toBean(llmRecord.getLlmOutput(), TagAnalysisDTO.class);
                tag.setLlmRecordId(llmRecord.getId());
                tag.setInput(llmRecord.getUserInput());
                List<TagAnalysisDTO.TopicSummary> customerQuestionSummaries = tag.getCustomerQuestionSummaries();
                // 过滤掉question或answer为空的记录
                customerQuestionSummaries.removeIf(summary -> StrUtil.isBlank(summary.getQuestion()) || StrUtil.isBlank(summary.getAnswer()));
                // 过滤掉question或answer过短的记录
                tag.setTopicSummaries(customerQuestionSummaries);
                tagData.add(tag);
            }
            responseDTO.setTagData(tagData);
            responseDTO.setTopicData(topicSummaryDTO);
            responseDTO.setRequestId(requestId);

            // 回调接口
            log.info("开始回调接口, 请求参数为: {}", JSONUtil.toJsonStr(responseDTO));
            DgwResult dgwResult = dgwFeignClient.callBackDmsEmotion(responseDTO);
            log.info("回调接口成功, 接口返回: {}", JSONUtil.toJsonStr(dgwResult));

            // 回调成功后，清理对应的message queue和llm record数据
            try {
                cleanDataAfterSuccess(requestId);
            } catch (Exception cleanException) {
                log.error("回调成功后清理数据失败，请求ID: {}", requestId, cleanException);
                // 清理失败不影响回调结果，只记录日志
            }

            return true;
        } catch (Exception e) {
            log.error("组装数据并发送给调用方失败，请求ID: {}", requestId, e);
            return false;
        }
    }

    /**
     * 处理成功后清理对应的message queue和llm record数据
     *
     * @param requestId 请求ID
     * <AUTHOR>
     */
    private void cleanDataAfterSuccess(String requestId) {
        log.info("开始清理处理成功的数据，请求ID: {}", requestId);

        // 查询该requestId对应的消息队列记录
        LambdaQueryWrapper<MessageQueue> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MessageQueue::getMessageContent, requestId);
        List<MessageQueue> messageQueues = messageQueueMapper.selectList(queryWrapper);

        if (CollUtil.isNotEmpty(messageQueues)) {
            // 删除llm_record表中对应的记录
            LambdaQueryWrapper<LlmRecord> llmQueryWrapper = new LambdaQueryWrapper<>();
            llmQueryWrapper.eq(LlmRecord::getRequestId, requestId);
            int deletedLlmRecords = llmRecordMapper.delete(llmQueryWrapper);
            log.info("成功清理请求ID: {} 对应的{}条llm_record记录", requestId, deletedLlmRecords);

            // 删除消息队列记录
            List<Long> ids = messageQueues.stream()
                    .map(MessageQueue::getId)
                    .collect(Collectors.toList());
            int deletedMessageQueues = messageQueueMapper.deleteBatchIds(ids);
            log.info("成功清理请求ID: {} 对应的{}条消息队列记录", requestId, deletedMessageQueues);
        } else {
            log.info("请求ID: {} 没有找到对应的已处理消息队列记录", requestId);
        }
    }

    private TopicSummaryDTO performTopicSummary(String requestId) {
        List<LlmRecord> all = llmRecordMapper.selectByRequestId(requestId, null, LLMBizTypeEnum.DMS_EMOTION.getCode());
        // 全文内容
        StringBuilder allContext = new StringBuilder();
        for (LlmRecord slice : all) {
            allContext.append(slice.getUserInput());
        }
        TopicSummaryDTO summaryDTO = BaiLianUtils.callForObject(
                baiLianAppConfig.getEmotionWorkspaceId(),
                baiLianAppConfig.getEmotionApiKey(),
                baiLianAppConfig.getEmotionTopicAppId(),
                allContext.toString(),
                TopicSummaryDTO.class
        );
        if (summaryDTO != null){
            summaryDTO.setInput(allContext.toString());
        }
        log.info("话题总结结果: {}", JSONUtil.toJsonStr(summaryDTO));
        return summaryDTO;
    }


    private void performEmotionAnalysis(LlmRecord emotion) {
        log.info("开始对记录ID: {} 进行情绪分析", emotion.getId());
        EmotionResponseDTO result = BaiLianUtils.callForObject(
                baiLianAppConfig.getEmotionWorkspaceId(),
                baiLianAppConfig.getEmotionApiKey(),
                baiLianAppConfig.getEmotionAnalysisAppId(),
                emotion.getUserInput(),
                EmotionResponseDTO.class);
        if (result != null) {
            LambdaUpdateWrapper<LlmRecord> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(LlmRecord::getId, emotion.getId())
                    .set(LlmRecord::getLlmOutput, JSONUtil.toJsonStr(result))
                    .set(LlmRecord::getField3, result.getStart())
                    .set(LlmRecord::getField4, result.getEnd())
            ;
            this.update(updateWrapper);
            log.info("记录ID: {} 情绪分析成功", emotion.getId());
        }


    }

    public String downloadAndReadCosFile(String fileUrl) throws IOException {
        URL url = new URL(fileUrl);
        byte[] fileContent;

        // Read file content
        try (InputStream inputStream = url.openStream()) {
            fileContent = readAllBytes(inputStream);
        }

        // Try to detect encoding automatically
        String detectedEncoding = detectEncoding(fileContent);
        return new String(fileContent, detectedEncoding);

    }

    private String detectEncoding(byte[] fileContent) {
        UniversalDetector detector = new UniversalDetector(null);
        detector.handleData(fileContent, 0, fileContent.length);
        detector.dataEnd();
        String encoding = detector.getDetectedCharset();
        detector.reset();
        return encoding;
    }

    private byte[] readAllBytes(InputStream inputStream) throws IOException {
        try (ByteArrayOutputStream buffer = new ByteArrayOutputStream()) {
            byte[] data = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(data, 0, data.length)) != -1) {
                buffer.write(data, 0, bytesRead);
            }
            return buffer.toByteArray();
        }
    }

    private void performTagExtraction(LlmRecord emotion) {
        log.info("开始对记录ID: {} 进行标签提取", emotion.getId());
        TagAnalysisDTO tagResult = BaiLianUtils.callForObject(
                baiLianAppConfig.getEmotionWorkspaceId(),
                baiLianAppConfig.getEmotionApiKey(),
                baiLianAppConfig.getEmotionTagAppId(),
                emotion.getUserInput(),
                TagAnalysisDTO.class
        );
        // LLM
        if (tagResult != null) {
            // String tagContent = formatTagContent(tagResult);
            LambdaUpdateWrapper<LlmRecord> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(LlmRecord::getId, emotion.getId())
                    .set(LlmRecord::getLlmOutput, JSONUtil.toJsonStr(tagResult))
                    .set(LlmRecord::getField3, tagResult.getStart())
                    .set(LlmRecord::getField4, tagResult.getEnd())
            ;
            this.update(updateWrapper);
            log.info("记录ID: {} 标签提取成功", emotion.getId());
        }
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    @Override
    public void updateStatusTran(Long id, String status, String remark) {
        LambdaUpdateWrapper<LlmRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(LlmRecord::getId, id)
                .set(LlmRecord::getStatus, status)
                .set(LlmRecord::getRemark, remark);
        this.update(updateWrapper);
    }

    @Override
    public boolean processSliceWithoutTransaction(String requestId) {
        log.info("开始处理分片数据（无事务版本），请求ID: {}", requestId);

        try {
            //查询失败和未处理的分片数据
            List<LlmRecord> list = llmRecordMapper.selectByRequestId(requestId, STATUS_UNPROCESSED, null);
            List<LlmRecord> list2 = llmRecordMapper.selectByRequestId(requestId, STATUS_FAILED, null);
            list.addAll(list2);

            if (CollUtil.isEmpty(list)) {
                log.info("没有找到未处理的分片数据，请求ID: {}", requestId);
                return true;
            }

            List<CompletableFuture<Void>> futures = new ArrayList<>();

            for (LlmRecord slice : list) {
                // 更新状态为处理中
                log.info("开始处理分片ID: {}", slice.getId());
                self.updateStatusTran(slice.getId(), STATUS_PROCESSING, null);

                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        // 串行执行情绪分析和标签提取
                        if (slice.getBizType().equals(LLMBizTypeEnum.DMS_EMOTION.getCode())) {
                            performEmotionAnalysis(slice);
                        }
                        if (slice.getBizType().equals(LLMBizTypeEnum.DMS_PRODUCT.getCode())) {
                            performTagExtraction(slice);
                        }

                        self.updateStatusTran(slice.getId(), STATUS_COMPLETED, null);
                        log.info("分片ID: {} 处理成功", slice.getId());
                    } catch (Exception e) {
                        log.error("分片ID: {} 处理失败，可能是大模型限流了", slice.getId(), e);
                        self.updateStatusTran(slice.getId(), STATUS_FAILED, null);
                    }
                }, dmsEmotionExecutor);

                futures.add(future);
            }

            // 等待所有异步任务完成
            try {
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            } catch (Exception e) {
                log.error("等待异步任务完成时发生异常", e);
                return false;
            }

            // 调用话题总结模型
            TopicSummaryDTO topicSummaryDTO = performTopicSummary(requestId);

            // 组装数据
            DmsEmotionResponse responseDTO = new DmsEmotionResponse();

            // 填充情绪分析结果
            EmotionResponseSummaryDTO emotion = new EmotionResponseSummaryDTO();
            emotion.setEmotionSummary(topicSummaryDTO.getTotalEmotion());

            List<LlmRecord> finishEmotionList = llmRecordMapper.selectByRequestId(requestId, STATUS_COMPLETED, LLMBizTypeEnum.DMS_EMOTION.getCode());
            List<EmotionResponseDTO> emotionList = new ArrayList<>();
            for (LlmRecord llmRecord : finishEmotionList) {
                EmotionResponseDTO emotionResponseDTO = JSONUtil.toBean(llmRecord.getLlmOutput(), EmotionResponseDTO.class);
                emotionResponseDTO.setInput(llmRecord.getUserInput());
                emotionList.add(emotionResponseDTO);
            }
            emotion.setEmotionList(emotionList);

            responseDTO.setEmotionData(emotion);

            // 填充标签提取结果
            List<LlmRecord> finishTagList = llmRecordMapper.selectByRequestId(requestId, STATUS_COMPLETED, LLMBizTypeEnum.DMS_PRODUCT.getCode());

            List<TagAnalysisDTO> tagData = new ArrayList<>();
            for (LlmRecord llmRecord : finishTagList) {
                TagAnalysisDTO tag = JSONUtil.toBean(llmRecord.getLlmOutput(), TagAnalysisDTO.class);
                tag.setLlmRecordId(llmRecord.getId());
                tag.setInput(llmRecord.getUserInput());
                List<TagAnalysisDTO.TopicSummary> customerQuestionSummaries = tag.getCustomerQuestionSummaries();
                // 过滤掉question或answer为空的记录
                customerQuestionSummaries.removeIf(summary -> StrUtil.isBlank(summary.getQuestion()) || StrUtil.isBlank(summary.getAnswer()));
                // 过滤掉question或answer过短的记录
                tag.setTopicSummaries(customerQuestionSummaries);
                tagData.add(tag);
            }
            responseDTO.setTagData(tagData);
            responseDTO.setTopicData(topicSummaryDTO);
            responseDTO.setRequestId(requestId);

            // 回调接口
            log.info("开始回调接口, 请求参数为: {}", JSONUtil.toJsonStr(responseDTO));

            DgwResult dgwResult = dgwFeignClient.callBackDmsEmotion(responseDTO);

            log.info("回调接口成功, 接口返回: {}", JSONUtil.toJsonStr(dgwResult));

        } catch (Exception e) {
            log.error("处理分片数据时发生异常，请求ID: {}", requestId, e);
            throw new BizException(e.getMessage(),e);
        }

        return true;
    }

    /**
     * 监控连接池状态（开发阶段使用）
     */
    private void logConnectionPoolStatus(String context) {
        try {
            if (dataSource instanceof HikariDataSource hikariDataSource) {
                HikariPoolMXBean poolBean = hikariDataSource.getHikariPoolMXBean();

                log.info("[连接池监控-{}] 总连接数: {}, 活跃连接: {}, 空闲连接: {}, 等待线程: {}",
                    context,
                    poolBean.getTotalConnections(),
                    poolBean.getActiveConnections(),
                    poolBean.getIdleConnections(),
                    poolBean.getThreadsAwaitingConnection());
            }
        } catch (Exception e) {
            log.warn("获取连接池状态失败: {}", e.getMessage());
        }
    }

    @Override
    public boolean retryFailedRecords(String requestId) {
        log.info("开始重试失败的记录，请求ID: {}", requestId);

        // 查询所有状态为失败的记录
        List<LlmRecord> failedRecords = llmRecordMapper.selectList(
                new LambdaQueryWrapper<LlmRecord>()
                        .eq(LlmRecord::getRequestId, requestId)
                        .eq(LlmRecord::getStatus, STATUS_FAILED)
        );

        if (CollUtil.isEmpty(failedRecords)) {
            log.info("没有找到失败的记录，请求ID: {}", requestId);

            // 检查是否所有记录都已成功，如果是则重新组装数据发送给调用方
            List<LlmRecord> allRecords = llmRecordMapper.selectByRequestId(requestId, null, null);
            if (CollUtil.isNotEmpty(allRecords)) {
                // 检查是否所有记录都是成功状态
                boolean allSuccess = allRecords.stream()
                        .allMatch(record -> STATUS_COMPLETED.equals(record.getStatus()));

                if (allSuccess) {
                    log.info("所有记录都已成功，重新组装数据发送给调用方，请求ID: {}", requestId);
                    try {
                        // 重新组装数据并发送
                        return assembleAndSendResponse(requestId);
                    } catch (Exception e) {
                        log.error("重新组装数据并发送失败，请求ID: {}", requestId, e);
                        return false;
                    }
                } else {
                    log.info("存在未完成的记录，请求ID: {}", requestId);
                }
            }

            return true;
        }

        // 更新状态为未处理
        for (LlmRecord record : failedRecords) {
            self.updateStatusTran(record.getId(), STATUS_UNPROCESSED, "重试任务");
        }

        // 直接调用processSlice处理
        try {
            boolean result = self.processSlice(requestId);
            if (result) {
                log.info("重试任务处理成功，请求ID: {}", requestId);

                // 重试处理成功后，清理对应的message queue和llm record数据
                try {
                    cleanDataAfterSuccess(requestId);
                } catch (Exception cleanException) {
                    log.error("重试成功后清理数据失败，请求ID: {}", requestId, cleanException);
                    // 清理失败不影响重试结果，只记录日志
                }

                return true;
            } else {
                log.error("重试任务处理失败，请求ID: {}", requestId);
                return false;
            }
        } catch (Exception e) {
            log.error("重试任务处理异常，请求ID: {}", requestId, e);
            return false;
        }
    }

    @Override
    public AiResponse testChat(AiRequest request) {

        // 1. 构建ChatClient请求，并设置用户输入
        ChatClient.ChatClientRequestSpec chatRequest = chatClient.prompt()
                .user(request.getUserInput());


        // 2. 如果有自定义prompt设置，则作为系统消息使用
        if (StrUtil.isNotBlank(request.getPrompt())) {
            chatRequest = chatRequest.system(request.getPrompt());
        }

        // 3. 设置模型、温度、最大长度等参数
        // 使用.options()来配置模型参数
        ChatOptions chatOptions = DashScopeChatOptions.builder().withModel(request.getModelName())
                .withTemperature(request.getTemperature())
                .withMaxToken(request.getMaxResponseLength()).build();
        chatRequest.options(chatOptions);

        // 4. 调用AI模型并获取非流式响应
        ChatResponse chatResponse = chatRequest.call().chatResponse();

        // 5. 构建成功的响应结果
        return AiResponse.builder()
                .content(chatResponse.getResult().getOutput().getText())
                .build();
    }


    /**
     * 清理指定请求ID的消息队列和分片数据
     *
     * @param messageId 请求ID
     * <AUTHOR>
     */
    @Override
    public void cleanRequestData(String messageId) {
        log.info("开始清理消息ID: {} 的消息队列和分片数据", messageId);

        // 查询该requestId对应的消息队列记录
        LambdaQueryWrapper<MessageQueue> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MessageQueue::getMessageId, messageId);
                // .eq(MessageQueue::getStatus, MessageStatus.PROCESSED.getCode());
        List<MessageQueue> messageQueues = messageQueueMapper.selectList(queryWrapper);

        // 删除llm_record表中对应的记录
        LambdaQueryWrapper<LlmRecord> llmQueryWrapper = new LambdaQueryWrapper<>();
        llmQueryWrapper.eq(LlmRecord::getRequestId, messageQueues.get(0).getMessageContent());
        int deletedLlmRecords = llmRecordMapper.delete(llmQueryWrapper);
        log.info("成功清理请求ID: {} 对应的{}条llm_record记录", messageId, deletedLlmRecords);

        // 删除消息队列记录
        if (CollUtil.isNotEmpty(messageQueues)) {
            List<Long> ids = messageQueues.stream()
                    .map(MessageQueue::getId)
                    .collect(Collectors.toList());
            int deletedMessageQueues = messageQueueMapper.deleteBatchIds(ids);
            log.info("成功清理请求ID: {} 对应的{}条消息队列记录", messageId, deletedMessageQueues);
        } else {
            log.info("请求ID: {} 没有找到对应的消息队列记录", messageId);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MessageQueueCleanResponse cleanCompletedMessages() {
        // 查询状态为2的消息队列数据
        LambdaQueryWrapper<MessageQueue> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MessageQueue::getStatus, MessageStatus.PROCESSED.getCode());
        List<MessageQueue> completedMessages = messageQueueMapper.selectList(queryWrapper);

        if (completedMessages.isEmpty()) {
            return MessageQueueCleanResponse.builder()
                    .cleanCount(0)
                    .requestIds(List.of())
                    .build();
        }

        // 获取所有的requestIds（即messageContent）
        List<String> requestIds = completedMessages.stream()
                .map(MessageQueue::getMessageContent)
                .collect(Collectors.toList());

        // 删除llm_record表中对应的记录
        if (!requestIds.isEmpty()) {
            LambdaQueryWrapper<LlmRecord> llmQueryWrapper = new LambdaQueryWrapper<>();
            llmQueryWrapper.in(LlmRecord::getRequestId, requestIds);
            llmRecordMapper.delete(llmQueryWrapper);
            log.info("成功清理{}个请求ID对应的llm_record记录", requestIds.size());
        }

        // 删除消息队列记录
        List<Long> ids = completedMessages.stream()
                .map(MessageQueue::getId)
                .collect(Collectors.toList());

        int deletedCount = messageQueueMapper.deleteBatchIds(ids);

        log.info("成功清理{}条已完成的消息队列数据", deletedCount);

        return MessageQueueCleanResponse.builder()
                .cleanCount(deletedCount)
                .requestIds(requestIds)
                .build();
    }

    @Override
    public RetryFailedMessagesResponse retryAllFailedMessages(RetryFailedMessagesRequest request) {
        log.info("开始重试所有失败的消息队列，清理成功数据: {}", request.getCleanSuccessData());

        // 监控连接池初始状态
        logConnectionPoolStatus("重试开始");

        // 1. 查询所有状态为10（失败）的消息队列
        LambdaQueryWrapper<MessageQueue> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MessageQueue::getStatus, MessageStatus.FAILED.getCode());
        List<MessageQueue> failedMessages = messageQueueMapper.selectList(queryWrapper);

        if (CollUtil.isEmpty(failedMessages)) {
            log.info("没有找到失败的消息队列");
            return RetryFailedMessagesResponse.builder()
                    .totalFailedMessages(0)
                    .successRetryCount(0)
                    .failedRetryCount(0)
                    .skippedCount(0)
                    .cleanedCount(0)
                    .successRequestIds(List.of())
                    .failedRequestIds(List.of())
                    .build();
        }

        log.info("找到{}条失败的消息队列", failedMessages.size());

        // 监控连接池状态
        logConnectionPoolStatus("查询完成");

        // 使用线程安全的集合来存储结果
        List<String> successRequestIds = Collections.synchronizedList(new ArrayList<>());
        List<String> failedRequestIds = Collections.synchronizedList(new ArrayList<>());
        AtomicInteger skippedCount = new AtomicInteger(0);
        AtomicInteger cleanedCount = new AtomicInteger(0);

        // 创建CompletableFuture列表来管理异步任务
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        for (MessageQueue messageQueue : failedMessages) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                String requestId = messageQueue.getMessageContent();
                log.info("开始重试请求ID: {}", requestId);

                try {
                    // 2. 根据messageContent查询对应的llm_record
                    List<LlmRecord> llmRecords = llmRecordMapper.selectByRequestId(requestId, null, null);

                    if (CollUtil.isEmpty(llmRecords)) {
                        log.warn("请求ID: {} 没有找到对应的llm_record记录", requestId);
                        failedRequestIds.add(requestId);
                        return;
                    }

                    // 3. 检查是否有需要处理的记录（状态为00或10）
                    List<LlmRecord> needRetryRecords = llmRecords.stream()
                            .filter(record -> STATUS_UNPROCESSED.equals(record.getStatus()) || STATUS_FAILED.equals(record.getStatus()))
                            .collect(Collectors.toList());

                    if (CollUtil.isEmpty(needRetryRecords)) {
                        log.info("请求ID: {} 的所有记录都已完成，跳过重试", requestId);
                        skippedCount.incrementAndGet();
                        return;
                    }

                    log.info("请求ID: {} 找到{}条需要重试的记录", requestId, needRetryRecords.size());

                    // 4. 更新消息队列状态为处理中
                    messageQueueService.updateStatus(messageQueue.getMessageId(), MessageStatus.PROCESSING, "重试处理");

                    // 5. 调用processSliceWithoutTransaction方法进行重试（避免事务冲突）
                    boolean retrySuccess = self.processSliceWithoutTransaction(requestId);

                    if (retrySuccess) {
                        log.info("请求ID: {} 重试成功", requestId);
                        successRequestIds.add(requestId);

                        // 6. 更新消息队列状态为已处理
                        messageQueueService.updateStatus(messageQueue.getMessageId(), MessageStatus.PROCESSED, "重试成功");

                        // 7. 如果需要清理数据
                        if (Boolean.TRUE.equals(request.getCleanSuccessData())) {
                            self.cleanRequestData(messageQueue.getMessageId());
                            cleanedCount.incrementAndGet();
                        }
                    } else {
                        log.error("请求ID: {} 重试失败", requestId);
                        failedRequestIds.add(requestId);
                        messageQueueService.updateStatus(messageQueue.getMessageId(), MessageStatus.FAILED, "重试失败");
                    }

                } catch (Exception e) {
                    log.error("重试请求ID: {} 时发生异常", requestId, e);
                    failedRequestIds.add(requestId);
                    messageQueueService.updateStatus(messageQueue.getMessageId(), MessageStatus.FAILED, "重试异常: " + e.getMessage());
                }
            }, retryExecutor);

            futures.add(future);
        }

        // 监控连接池状态（任务提交后）
        logConnectionPoolStatus("任务提交完成");

        // 等待所有任务完成
        try {
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get();
            log.info("所有重试任务已完成");

            // 监控连接池状态（任务完成后）
            logConnectionPoolStatus("任务执行完成");
        } catch (Exception e) {
            log.error("等待重试任务完成时发生异常", e);
            logConnectionPoolStatus("任务执行异常");
        }

        RetryFailedMessagesResponse response = RetryFailedMessagesResponse.builder()
                .totalFailedMessages(failedMessages.size())
                .successRetryCount(successRequestIds.size())
                .failedRetryCount(failedRequestIds.size())
                .skippedCount(skippedCount.get())
                .cleanedCount(cleanedCount.get())
                .successRequestIds(successRequestIds)
                .failedRequestIds(failedRequestIds)
                .build();

        log.info("重试完成，总计: {}, 成功: {}, 失败: {}, 跳过: {}, 清理: {}",
                response.getTotalFailedMessages(),
                response.getSuccessRetryCount(),
                response.getFailedRetryCount(),
                response.getSkippedCount(),
                response.getCleanedCount());

        // 监控连接池最终状态
        logConnectionPoolStatus("重试结束");

        return response;
    }
}
